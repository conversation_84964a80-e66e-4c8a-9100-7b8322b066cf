@extends('layouts.app')

@section('title', 'Search Results - Food Ordering App')

@section('content')
<!-- Quick Filters Slider (below nav bar) -->
<div class="bg-white border-b border-gray-200 sticky top-16 z-40">
    <div class="container-mobile">
        <div class="py-3">
            <div class="flex space-x-2 overflow-x-auto pb-2">
                <!-- All Categories -->
                <a href="{{ route('search') }}" class="filter-btn {{ !$categoryId ? 'bg-orange-100 text-orange-800' : 'bg-gray-100 text-gray-800' }} px-4 py-2 rounded-full whitespace-nowrap hover:bg-orange-200 transition-colors">
                    <i class="fas fa-utensils mr-1"></i> All Categories
                </a>

                <!-- Dynamic Main Categories -->
                @foreach($mainCategories as $category)
                    <a href="{{ route('search', array_merge(request()->query(), ['category' => $category->id, 'subcategory' => null])) }}"
                       class="filter-btn category-filter {{ $categoryId == $category->id ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800' }} px-4 py-2 rounded-full whitespace-nowrap hover:bg-blue-200 transition-colors"
                       data-category-id="{{ $category->id }}">
                        @if($category->image)
                            <img src="{{ $category->image }}" alt="{{ $category->name }}" class="w-4 h-4 inline mr-1 rounded-full">
                        @else
                            <i class="fas fa-utensils mr-1"></i>
                        @endif
                        {{ $category->name }}
                    </a>
                @endforeach

                <!-- Quick Filters -->
                <a href="{{ route('search', ['vegetarian' => 1]) }}" class="filter-btn bg-green-100 text-green-800 px-4 py-2 rounded-full whitespace-nowrap hover:bg-green-200 transition-colors">
                    <i class="fas fa-leaf mr-1"></i> Vegetarian
                </a>
                <a href="{{ route('search', ['popular' => 1]) }}" class="filter-btn bg-yellow-100 text-yellow-800 px-4 py-2 rounded-full whitespace-nowrap hover:bg-yellow-200 transition-colors">
                    <i class="fas fa-star mr-1"></i> Popular
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Advanced Left Sidebar Filter (like real food apps) -->
<div id="filter-sidebar" class="fixed left-0 top-16 h-full bg-white shadow-xl border-r border-gray-200 z-30 transform transition-transform duration-300 ease-in-out">
    <!-- Sidebar Header -->
    <div class="bg-gradient-to-r from-orange-500 to-red-500 text-white p-2">
        <div>
            <h3 class="font-bold text-sm">Filters</h3>
            <p class="text-orange-100 text-xs hidden sm:block">Find what you want</p>
        </div>
    </div>

    <!-- Sidebar Content -->
    <div class="overflow-y-auto h-full pb-20">

        <!-- Categories Section -->
        @if($mainCategories->count() > 0)
        <div class="p-2 border-b border-gray-100">
            <h4 class="font-semibold text-gray-800 mb-1 text-xs">
                Categories
            </h4>
            <div class="space-y-0.5">
                <a href="{{ route('search', array_diff_key(request()->query(), ['category' => '', 'subcategory' => ''])) }}"
                   class="block w-full text-left p-1.5 rounded text-xs {{ !$categoryId ? 'bg-orange-100 text-orange-800' : 'text-gray-700 hover:bg-gray-100' }} transition-all duration-200">
                    All Categories
                </a>

                @foreach($mainCategories as $category)
                    <a href="{{ route('search', array_merge(request()->query(), ['category' => $category->id, 'subcategory' => null])) }}"
                       class="block w-full text-left p-1.5 rounded text-xs {{ $categoryId == $category->id ? 'bg-blue-100 text-blue-800' : 'text-gray-700 hover:bg-gray-100' }} transition-all duration-200">
                        {{ $category->name }}
                        @if($categoryId == $category->id && $subcategories->count() > 0)
                            <span class="float-right">▼</span>
                        @endif
                    </a>
                @endforeach
            </div>
        </div>
        @endif

        <!-- Subcategories Section -->
        @if($subcategories->count() > 0)
        <div class="p-2 border-b border-gray-100">
            <h4 class="font-semibold text-gray-800 mb-1 text-xs">
                Subcategories
                <span class="ml-auto text-xs bg-blue-100 text-blue-800 px-1 py-0.5 rounded">{{ $subcategories->count() }}</span>
            </h4>
            <div class="space-y-0.5">
                <a href="{{ route('search', array_merge(request()->query(), ['subcategory' => null])) }}"
                   class="block w-full text-left p-1.5 rounded text-xs {{ !$subcategoryId ? 'bg-blue-100 text-blue-800' : 'text-gray-700 hover:bg-gray-100' }} transition-all duration-200">
                    All in Category
                </a>

                @foreach($subcategories as $subcategory)
                    <a href="{{ route('search', array_merge(request()->query(), ['subcategory' => $subcategory->id])) }}"
                       class="block w-full text-left p-1.5 rounded text-xs {{ $subcategoryId == $subcategory->id ? 'bg-indigo-100 text-indigo-800' : 'text-gray-700 hover:bg-gray-100' }} transition-all duration-200">
                        {{ $subcategory->name }}
                    </a>
                @endforeach
            </div>
        </div>
        @endif

        <!-- Quick Filters Section -->
        <div class="p-2 border-b border-gray-100">
            <h4 class="font-semibold text-gray-800 mb-1 text-xs">
                Quick Filters
            </h4>
            <div class="space-y-0.5">
                <a href="{{ route('search', array_merge(request()->query(), ['vegetarian' => $isVegetarian ? null : 1])) }}"
                   class="block w-full text-left p-1.5 rounded text-xs {{ $isVegetarian ? 'bg-green-100 text-green-800' : 'text-gray-700 hover:bg-gray-100' }} transition-all duration-200">
                    Vegetarian Only
                    @if($isVegetarian)
                        <span class="float-right">✓</span>
                    @endif
                </a>

                <a href="{{ route('search', array_merge(request()->query(), ['popular' => $isPopular ? null : 1])) }}"
                   class="block w-full text-left p-1.5 rounded text-xs {{ $isPopular ? 'bg-yellow-100 text-yellow-800' : 'text-gray-700 hover:bg-gray-100' }} transition-all duration-200">
                    Popular Items
                    @if($isPopular)
                        <span class="float-right">✓</span>
                    @endif
                </a>
            </div>
        </div>

        <!-- Cuisines Section -->
        @if($cuisines->count() > 0)
        <div class="p-2 border-b border-gray-100">
            <h4 class="font-semibold text-gray-800 mb-1 text-xs">
                Cuisines
            </h4>
            <div class="space-y-0.5 max-h-24 overflow-y-auto">
                <a href="{{ route('search', array_diff_key(request()->query(), ['cuisine' => ''])) }}"
                   class="block w-full text-left p-1.5 rounded text-xs {{ !$cuisineId ? 'bg-purple-100 text-purple-800' : 'text-gray-700 hover:bg-gray-100' }} transition-all duration-200">
                    All Cuisines
                </a>

                @foreach($cuisines as $cuisine)
                    <a href="{{ route('search', array_merge(request()->query(), ['cuisine' => $cuisine->id])) }}"
                       class="block w-full text-left p-1.5 rounded text-xs {{ $cuisineId == $cuisine->id ? 'bg-purple-100 text-purple-800' : 'text-gray-700 hover:bg-gray-100' }} transition-all duration-200">
                        {{ $cuisine->name }}
                    </a>
                @endforeach
            </div>
        </div>
        @endif

        <!-- Clear Filters -->
        @if($query || $categoryId || $subcategoryId || $cuisineId || $isVegetarian || $isPopular)
        <div class="p-2">
            <a href="{{ route('search') }}"
               class="block w-full text-center p-1.5 bg-red-100 text-red-800 rounded hover:bg-red-200 transition-colors text-xs">
                Clear All Filters
            </a>
        </div>
        @endif
    </div>
</div>

<!-- Mobile Overlay (not needed since sidebar is always visible) -->
<!-- Mobile Filter Toggle Button (not needed since sidebar is always visible) -->

<div class="transition-all duration-300" id="main-content">
    <div class="container-mobile">
    <!-- Page Header -->
    <div class="py-6">
        <h1 class="text-2xl font-bold mb-4">
            @if($query)
                Search Results for "{{ $query }}"
            @else
                Browse Menu
            @endif
        </h1>



        <!-- Active Filters Display -->
        @if($query || $categoryId || $subcategoryId || $cuisineId || $isVegetarian || $isPopular)
            <div class="mb-4">
                <div class="flex flex-wrap gap-2 items-center">
                    <span class="text-sm text-gray-600">Active filters:</span>

                    @if($query)
                        <span class="bg-orange-100 text-orange-800 px-3 py-1 rounded-full text-sm">
                            Search: "{{ $query }}"
                            <a href="{{ route('search', array_diff_key(request()->query(), ['q' => ''])) }}" class="ml-1 text-orange-600 hover:text-orange-800">×</a>
                        </span>
                    @endif

                    @if($categoryId)
                        @php $category = $mainCategories->find($categoryId); @endphp
                        @if($category)
                            <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm">
                                Category: {{ $category->name }}
                                <a href="{{ route('search', array_diff_key(request()->query(), ['category' => '', 'subcategory' => ''])) }}" class="ml-1 text-blue-600 hover:text-blue-800">×</a>
                            </span>
                        @endif
                    @endif

                    @if($subcategoryId)
                        @php $subcategory = $subcategories->find($subcategoryId); @endphp
                        @if($subcategory)
                            <span class="bg-indigo-100 text-indigo-800 px-3 py-1 rounded-full text-sm">
                                Subcategory: {{ $subcategory->name }}
                                <a href="{{ route('search', array_diff_key(request()->query(), ['subcategory' => ''])) }}" class="ml-1 text-indigo-600 hover:text-indigo-800">×</a>
                            </span>
                        @endif
                    @endif

                    @if($cuisineId)
                        @php $cuisine = $cuisines->find($cuisineId); @endphp
                        @if($cuisine)
                            <span class="bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm">
                                Cuisine: {{ $cuisine->name }}
                                <a href="{{ route('search', array_diff_key(request()->query(), ['cuisine' => ''])) }}" class="ml-1 text-purple-600 hover:text-purple-800">×</a>
                            </span>
                        @endif
                    @endif

                    @if($isVegetarian)
                        <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm">
                            <i class="fas fa-leaf mr-1"></i>Vegetarian
                            <a href="{{ route('search', array_diff_key(request()->query(), ['vegetarian' => ''])) }}" class="ml-1 text-green-600 hover:text-green-800">×</a>
                        </span>
                    @endif

                    @if($isPopular)
                        <span class="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm">
                            <i class="fas fa-star mr-1"></i>Popular
                            <a href="{{ route('search', array_diff_key(request()->query(), ['popular' => ''])) }}" class="ml-1 text-yellow-600 hover:text-yellow-800">×</a>
                        </span>
                    @endif

                    <a href="{{ route('search') }}" class="text-orange-600 text-sm font-medium hover:text-orange-800">
                        <i class="fas fa-times mr-1"></i>Clear All
                    </a>
                </div>
            </div>
        @endif


    </div>

    <!-- Results Summary -->
    <div class="mb-6">
        <div class="flex items-center justify-between">
            <div class="text-sm text-gray-600">
                Found {{ $foodItems->total() + $packages->total() }} results
                ({{ $foodItems->total() }} food items, {{ $packages->total() }} packages)
            </div>
        </div>
    </div>

    <!-- Menu Tabs -->
    <div class="mb-6">
        <div class="flex border-b border-gray-200">
            <button onclick="showTab('packages')" id="packages-tab" class="tab-btn px-4 py-2 font-medium text-gray-500 border-b-2 border-transparent hover:text-gray-700 hover:border-gray-300">
                Packages ({{ $packages->total() }})
            </button>
            <button onclick="showTab('food-items')" id="food-items-tab" class="tab-btn px-4 py-2 font-medium text-orange-600 border-b-2 border-orange-600">
                Food Items ({{ $foodItems->total() }})
            </button>
        </div>
    </div>

    <!-- Packages Section -->
    <div id="packages-section" class="hidden">
        @if($packages->count() > 0)
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                @foreach($packages as $package)
                <div class="food-card bg-white rounded-lg shadow-sm overflow-hidden">
                    <div class="relative">
                        @if($package->image)
                            <img src="{{ $package->image }}" alt="{{ $package->name }}" class="w-full h-48 object-cover">
                        @else
                            <div class="w-full h-48 bg-gray-200 flex items-center justify-center">
                                <i class="fas fa-image text-gray-400 text-3xl"></i>
                            </div>
                        @endif
                        @if($package->is_popular)
                            <span class="absolute top-2 left-2 bg-yellow-500 text-white px-2 py-1 rounded text-xs font-semibold">
                                <i class="fas fa-star mr-1"></i>Popular
                            </span>
                        @endif
                        @if($package->discount_percentage > 0)
                            <span class="absolute top-2 right-2 bg-red-500 text-white px-2 py-1 rounded text-xs font-semibold">
                                {{ $package->discount_percentage }}% OFF
                            </span>
                        @endif
                    </div>
                    <div class="p-4">
                        <div class="flex items-start justify-between mb-2">
                            <h3 class="font-semibold text-lg">{{ $package->name }}</h3>
                            <div class="flex space-x-1">
                                @if($package->is_vegetarian)
                                    <span class="bg-green-100 text-green-800 px-2 py-1 rounded text-xs">
                                        <i class="fas fa-leaf"></i>
                                    </span>
                                @endif
                            </div>
                        </div>
                        <p class="text-gray-600 text-sm mb-3">{{ $package->description }}</p>

                        <!-- Package Items Preview -->
                        @if($package->foodItems && $package->foodItems->count() > 0)
                            <div class="mb-3">
                                <p class="text-xs text-gray-500 mb-1">Includes:</p>
                                <div class="text-xs text-gray-600">
                                    @foreach($package->foodItems->take(3) as $item)
                                        <span>{{ $item->name }}{{ !$loop->last ? ', ' : '' }}</span>
                                    @endforeach
                                    @if($package->foodItems->count() > 3)
                                        <span class="text-orange-600">+{{ $package->foodItems->count() - 3 }} more</span>
                                    @endif
                                </div>
                            </div>
                        @endif

                        <div class="flex items-center justify-between">
                            <div>
                                <div class="flex items-center space-x-2">
                                    <span class="text-xl font-bold text-orange-600">${{ number_format($package->price, 2) }}</span>
                                    @if($package->original_price && $package->original_price > $package->price)
                                        <span class="text-sm text-gray-500 line-through">${{ number_format($package->original_price, 2) }}</span>
                                    @endif
                                </div>
                                @if($package->serves_people)
                                    <p class="text-xs text-gray-500">Serves {{ $package->serves_people }} people</p>
                                @endif
                            </div>
                            <div class="flex space-x-2">
                                <a href="{{ route('menu.package', $package->slug) }}" class="bg-gray-100 text-gray-700 px-3 py-2 rounded text-sm hover:bg-gray-200 transition-colors">
                                    View Details
                                </a>
                                <button onclick="addToCart('package', {{ $package->id }})" class="bg-orange-600 text-white px-4 py-2 rounded text-sm hover:bg-orange-700 transition-colors">
                                    Add to Cart
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>

            <!-- Packages Pagination -->
            <div class="mb-8">
                {{ $packages->appends(request()->query())->links() }}
            </div>
        @else
            <div class="text-center py-12">
                <i class="fas fa-box-open text-gray-400 text-4xl mb-4"></i>
                <p class="text-gray-600">No packages found matching your search criteria.</p>
                <a href="{{ route('search') }}" class="text-orange-600 hover:text-orange-800 mt-2 inline-block">Clear filters and browse all packages</a>
            </div>
        @endif
    </div>

    <!-- Food Items Section -->
    <div id="food-items-section">
        @if($foodItems->count() > 0)
            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mb-8">
                @foreach($foodItems as $item)
                <div class="food-card bg-white rounded-lg shadow-sm overflow-hidden">
                    <div class="relative">
                        @if($item->image)
                            <img src="{{ $item->image }}" alt="{{ $item->name }}" class="w-full h-32 object-cover">
                        @else
                            <div class="w-full h-32 bg-gray-200 flex items-center justify-center">
                                <i class="fas fa-image text-gray-400"></i>
                            </div>
                        @endif
                        <div class="absolute top-1 left-1 flex space-x-1">
                            @if($item->is_vegetarian)
                                <span class="bg-green-500 text-white px-1 py-0.5 rounded text-xs">
                                    <i class="fas fa-leaf"></i>
                                </span>
                            @endif
                            @if($item->is_popular)
                                <span class="bg-yellow-500 text-white px-1 py-0.5 rounded text-xs">
                                    <i class="fas fa-star"></i>
                                </span>
                            @endif
                        </div>
                        @if($item->is_spicy)
                            <span class="absolute top-1 right-1 bg-red-500 text-white px-1 py-0.5 rounded text-xs">
                                <i class="fas fa-pepper-hot"></i>
                            </span>
                        @endif
                    </div>
                    <div class="p-3">
                        <h3 class="font-semibold text-sm mb-1">{{ $item->name }}</h3>
                        <p class="text-xs text-gray-600 mb-2">{{ $item->category->name }} • {{ $item->cuisine->name }}</p>
                        <div class="flex items-center justify-between">
                            <div>
                                <span class="text-sm font-bold text-orange-600">${{ number_format($item->price_per_unit, 2) }}</span>
                                @if($item->allow_bulk_order && $item->price_per_kg)
                                    <span class="text-xs text-gray-500 block">${{ number_format($item->price_per_kg, 2) }}/kg</span>
                                @endif
                            </div>
                            <div class="flex space-x-1">
                                <a href="{{ route('menu.food-item', $item->slug) }}" class="bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs hover:bg-gray-200 transition-colors">
                                    View
                                </a>
                                <button onclick="addToCart('food_item', {{ $item->id }})" class="bg-orange-600 text-white px-2 py-1 rounded text-xs hover:bg-orange-700 transition-colors">
                                    Add
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>

            <!-- Food Items Pagination -->
            <div class="mb-8">
                {{ $foodItems->appends(request()->query())->links() }}
            </div>
        @else
            <div class="text-center py-12">
                <i class="fas fa-utensils text-gray-400 text-4xl mb-4"></i>
                <p class="text-gray-600">No food items found matching your search criteria.</p>
                <a href="{{ route('search') }}" class="text-orange-600 hover:text-orange-800 mt-2 inline-block">Clear filters and browse all items</a>
            </div>
        @endif
    </div>
    </div>
</div>

@push('scripts')
<script>
    // Tab functionality
    function showTab(tabName) {
        // Hide all sections
        document.getElementById('packages-section').classList.add('hidden');
        document.getElementById('food-items-section').classList.add('hidden');

        // Remove active class from all tabs
        document.getElementById('packages-tab').classList.remove('text-orange-600', 'border-orange-600');
        document.getElementById('packages-tab').classList.add('text-gray-500', 'border-transparent');
        document.getElementById('food-items-tab').classList.remove('text-orange-600', 'border-orange-600');
        document.getElementById('food-items-tab').classList.add('text-gray-500', 'border-transparent');

        // Show selected section and activate tab
        if (tabName === 'packages') {
            document.getElementById('packages-section').classList.remove('hidden');
            document.getElementById('packages-tab').classList.add('text-orange-600', 'border-orange-600');
            document.getElementById('packages-tab').classList.remove('text-gray-500', 'border-transparent');
        } else {
            document.getElementById('food-items-section').classList.remove('hidden');
            document.getElementById('food-items-tab').classList.add('text-orange-600', 'border-orange-600');
            document.getElementById('food-items-tab').classList.remove('text-gray-500', 'border-transparent');
        }
    }

    // Filter update function
    function updateFilter(param, value) {
        const url = new URL(window.location);
        if (value && value !== '') {
            url.searchParams.set(param, value);
        } else {
            url.searchParams.delete(param);
        }
        window.location.href = url.toString();
    }

    // Advanced Filter Sidebar functionality (always visible now)
    function initializeFilterSidebar() {
        const sidebar = document.getElementById('filter-sidebar');
        if (sidebar) {
            // Ensure sidebar is always visible
            sidebar.classList.remove('-translate-x-full');
            sidebar.classList.add('translate-x-0');
        }
    }

    // Initialize default tab and filter sidebar
    document.addEventListener('DOMContentLoaded', function() {
        showTab('food-items');

        // Initialize filter sidebar (always visible)
        initializeFilterSidebar();
    });
</script>
@endpush
@endsection
