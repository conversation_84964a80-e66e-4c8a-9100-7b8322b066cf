<?php $__env->startSection('title', 'Search Results - Food Ordering App'); ?>

<?php $__env->startPush('styles'); ?>
<style>
    /* Search page specific styles */
    .search-page-container {
        padding-left: 0 !important;
        margin-left: 0 !important;
    }

    /* Override container padding for search page */
    .search-page .container-mobile {
        padding-left: 1rem !important;
        padding-right: 1rem !important;
    }

    /* Ensure main content has proper spacing from sidebar */
    .search-main-content {
        transition: margin-left 0.3s ease;
    }

    @media (max-width: 767px) {
        .search-main-content {
            margin-left: calc(16.666667vw + 0.5rem) !important;
        }
    }

    @media (min-width: 768px) and (max-width: 1023px) {
        .search-main-content {
            margin-left: calc(16.666667vw + 0.5rem) !important;
        }
    }

    @media (min-width: 1024px) {
        .search-main-content {
            margin-left: calc(16.666667vw + 0.5rem) !important;
        }
    }

    /* Ensure sticky footer is always visible */
    body.search-page {
        padding-bottom: 80px !important;
    }

    /* Enhanced product grid spacing */
    .search-products-grid {
        padding: 0.5rem;
        gap: 0.75rem;
    }

    @media (max-width: 767px) {
        .search-products-grid {
            grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
        }
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<!-- Quick Filters Slider (below nav bar) -->
<div class="bg-white border-b border-gray-200 sticky top-16 z-40">
    <div class="container-mobile">
        <div class="py-3">
            <div class="flex space-x-2 overflow-x-auto pb-2">
                <!-- All Categories -->
                <a href="<?php echo e(route('search')); ?>" class="filter-btn <?php echo e(!$categoryId ? 'bg-orange-100 text-orange-800' : 'bg-gray-100 text-gray-800'); ?> px-4 py-2 rounded-full whitespace-nowrap hover:bg-orange-200 transition-colors">
                    <i class="fas fa-utensils mr-1"></i> All Categories
                </a>

                <!-- Dynamic Main Categories -->
                <?php $__currentLoopData = $mainCategories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <a href="<?php echo e(route('search', array_merge(request()->query(), ['category' => $category->id, 'subcategory' => null]))); ?>"
                       class="filter-btn category-filter <?php echo e($categoryId == $category->id ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'); ?> px-4 py-2 rounded-full whitespace-nowrap hover:bg-blue-200 transition-colors"
                       data-category-id="<?php echo e($category->id); ?>">
                        <?php if($category->image): ?>
                            <img src="<?php echo e($category->image); ?>" alt="<?php echo e($category->name); ?>" class="w-4 h-4 inline mr-1 rounded-full">
                        <?php else: ?>
                            <i class="fas fa-utensils mr-1"></i>
                        <?php endif; ?>
                        <?php echo e($category->name); ?>

                    </a>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                <!-- Quick Filters -->
                <a href="<?php echo e(route('search', ['vegetarian' => 1])); ?>" class="filter-btn bg-green-100 text-green-800 px-4 py-2 rounded-full whitespace-nowrap hover:bg-green-200 transition-colors">
                    <i class="fas fa-leaf mr-1"></i> Vegetarian
                </a>
                <a href="<?php echo e(route('search', ['popular' => 1])); ?>" class="filter-btn bg-yellow-100 text-yellow-800 px-4 py-2 rounded-full whitespace-nowrap hover:bg-yellow-200 transition-colors">
                    <i class="fas fa-star mr-1"></i> Popular
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Advanced Left Sidebar Filter (like real food apps) -->
<div id="filter-sidebar" class="fixed left-0 top-16 h-full bg-white shadow-xl border-r border-gray-200 z-30 transform transition-transform duration-300 ease-in-out">
    <!-- Sidebar Header -->
    <div class="sidebar-header text-white p-2">
        <div class="relative z-10">
            <h3 class="font-bold text-sm">Filters</h3>
            <p class="text-orange-100 text-xs hidden sm:block">Find what you want</p>
        </div>
    </div>

    <!-- Sidebar Content -->
    <div class="overflow-y-auto h-full pb-20">

        <!-- Categories Section -->
        <?php if($mainCategories->count() > 0): ?>
        <div class="p-2 border-b border-gray-100">
            <h4 class="font-semibold text-gray-800 mb-1 text-xs">
                Categories
            </h4>
            <div class="space-y-0.5">
                <a href="<?php echo e(route('search', array_diff_key(request()->query(), ['category' => '', 'subcategory' => '']))); ?>"
                   class="block w-full text-left p-1.5 rounded text-xs <?php echo e(!$categoryId ? 'bg-orange-100 text-orange-800' : 'text-gray-700 hover:bg-gray-100'); ?> transition-all duration-200">
                    All Categories
                </a>

                <?php $__currentLoopData = $mainCategories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <a href="<?php echo e(route('search', array_merge(request()->query(), ['category' => $category->id, 'subcategory' => null]))); ?>"
                       class="block w-full text-left p-1.5 rounded text-xs <?php echo e($categoryId == $category->id ? 'bg-blue-100 text-blue-800' : 'text-gray-700 hover:bg-gray-100'); ?> transition-all duration-200">
                        <?php echo e($category->name); ?>

                        <?php if($categoryId == $category->id && $subcategories->count() > 0): ?>
                            <span class="float-right">▼</span>
                        <?php endif; ?>
                    </a>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- Subcategories Section -->
        <?php if($subcategories->count() > 0): ?>
        <div class="p-2 border-b border-gray-100">
            <h4 class="font-semibold text-gray-800 mb-1 text-xs">
                Subcategories
                <span class="ml-auto text-xs bg-blue-100 text-blue-800 px-1 py-0.5 rounded"><?php echo e($subcategories->count()); ?></span>
            </h4>
            <div class="space-y-0.5">
                <a href="<?php echo e(route('search', array_merge(request()->query(), ['subcategory' => null]))); ?>"
                   class="block w-full text-left p-1.5 rounded text-xs <?php echo e(!$subcategoryId ? 'bg-blue-100 text-blue-800' : 'text-gray-700 hover:bg-gray-100'); ?> transition-all duration-200">
                    All in Category
                </a>

                <?php $__currentLoopData = $subcategories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $subcategory): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <a href="<?php echo e(route('search', array_merge(request()->query(), ['subcategory' => $subcategory->id]))); ?>"
                       class="block w-full text-left p-1.5 rounded text-xs <?php echo e($subcategoryId == $subcategory->id ? 'bg-indigo-100 text-indigo-800' : 'text-gray-700 hover:bg-gray-100'); ?> transition-all duration-200">
                        <?php echo e($subcategory->name); ?>

                    </a>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- Quick Filters Section -->
        <div class="p-2 border-b border-gray-100">
            <h4 class="font-semibold text-gray-800 mb-1 text-xs">
                Quick Filters
            </h4>
            <div class="space-y-0.5">
                <a href="<?php echo e(route('search', array_merge(request()->query(), ['vegetarian' => $isVegetarian ? null : 1]))); ?>"
                   class="block w-full text-left p-1.5 rounded text-xs <?php echo e($isVegetarian ? 'bg-green-100 text-green-800' : 'text-gray-700 hover:bg-gray-100'); ?> transition-all duration-200">
                    Vegetarian Only
                    <?php if($isVegetarian): ?>
                        <span class="float-right">✓</span>
                    <?php endif; ?>
                </a>

                <a href="<?php echo e(route('search', array_merge(request()->query(), ['popular' => $isPopular ? null : 1]))); ?>"
                   class="block w-full text-left p-1.5 rounded text-xs <?php echo e($isPopular ? 'bg-yellow-100 text-yellow-800' : 'text-gray-700 hover:bg-gray-100'); ?> transition-all duration-200">
                    Popular Items
                    <?php if($isPopular): ?>
                        <span class="float-right">✓</span>
                    <?php endif; ?>
                </a>
            </div>
        </div>

        <!-- Cuisines Section -->
        <?php if($cuisines->count() > 0): ?>
        <div class="p-2 border-b border-gray-100">
            <h4 class="font-semibold text-gray-800 mb-1 text-xs">
                Cuisines
            </h4>
            <div class="space-y-0.5 max-h-24 overflow-y-auto">
                <a href="<?php echo e(route('search', array_diff_key(request()->query(), ['cuisine' => '']))); ?>"
                   class="block w-full text-left p-1.5 rounded text-xs <?php echo e(!$cuisineId ? 'bg-purple-100 text-purple-800' : 'text-gray-700 hover:bg-gray-100'); ?> transition-all duration-200">
                    All Cuisines
                </a>

                <?php $__currentLoopData = $cuisines; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $cuisine): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <a href="<?php echo e(route('search', array_merge(request()->query(), ['cuisine' => $cuisine->id]))); ?>"
                       class="block w-full text-left p-1.5 rounded text-xs <?php echo e($cuisineId == $cuisine->id ? 'bg-purple-100 text-purple-800' : 'text-gray-700 hover:bg-gray-100'); ?> transition-all duration-200">
                        <?php echo e($cuisine->name); ?>

                    </a>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- Clear Filters -->
        <?php if($query || $categoryId || $subcategoryId || $cuisineId || $isVegetarian || $isPopular): ?>
        <div class="p-2">
            <a href="<?php echo e(route('search')); ?>"
               class="block w-full text-center p-1.5 bg-red-100 text-red-800 rounded hover:bg-red-200 transition-colors text-xs">
                Clear All Filters
            </a>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- Mobile Overlay (not needed since sidebar is always visible) -->
<!-- Mobile Filter Toggle Button (not needed since sidebar is always visible) -->

<div class="transition-all duration-300 search-main-content" id="main-content">
    <div class="container-mobile search-page-container">
    <!-- Page Header -->
    <div class="py-6">
        <h1 class="text-2xl font-bold mb-4">
            <?php if($query): ?>
                Search Results for "<?php echo e($query); ?>"
            <?php else: ?>
                Browse Menu
            <?php endif; ?>
        </h1>



        <!-- Active Filters Display -->
        <?php if($query || $categoryId || $subcategoryId || $cuisineId || $isVegetarian || $isPopular): ?>
            <div class="mb-4">
                <div class="flex flex-wrap gap-2 items-center">
                    <span class="text-sm text-gray-600">Active filters:</span>

                    <?php if($query): ?>
                        <span class="bg-orange-100 text-orange-800 px-3 py-1 rounded-full text-sm">
                            Search: "<?php echo e($query); ?>"
                            <a href="<?php echo e(route('search', array_diff_key(request()->query(), ['q' => '']))); ?>" class="ml-1 text-orange-600 hover:text-orange-800">×</a>
                        </span>
                    <?php endif; ?>

                    <?php if($categoryId): ?>
                        <?php $category = $mainCategories->find($categoryId); ?>
                        <?php if($category): ?>
                            <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm">
                                Category: <?php echo e($category->name); ?>

                                <a href="<?php echo e(route('search', array_diff_key(request()->query(), ['category' => '', 'subcategory' => '']))); ?>" class="ml-1 text-blue-600 hover:text-blue-800">×</a>
                            </span>
                        <?php endif; ?>
                    <?php endif; ?>

                    <?php if($subcategoryId): ?>
                        <?php $subcategory = $subcategories->find($subcategoryId); ?>
                        <?php if($subcategory): ?>
                            <span class="bg-indigo-100 text-indigo-800 px-3 py-1 rounded-full text-sm">
                                Subcategory: <?php echo e($subcategory->name); ?>

                                <a href="<?php echo e(route('search', array_diff_key(request()->query(), ['subcategory' => '']))); ?>" class="ml-1 text-indigo-600 hover:text-indigo-800">×</a>
                            </span>
                        <?php endif; ?>
                    <?php endif; ?>

                    <?php if($cuisineId): ?>
                        <?php $cuisine = $cuisines->find($cuisineId); ?>
                        <?php if($cuisine): ?>
                            <span class="bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm">
                                Cuisine: <?php echo e($cuisine->name); ?>

                                <a href="<?php echo e(route('search', array_diff_key(request()->query(), ['cuisine' => '']))); ?>" class="ml-1 text-purple-600 hover:text-purple-800">×</a>
                            </span>
                        <?php endif; ?>
                    <?php endif; ?>

                    <?php if($isVegetarian): ?>
                        <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm">
                            <i class="fas fa-leaf mr-1"></i>Vegetarian
                            <a href="<?php echo e(route('search', array_diff_key(request()->query(), ['vegetarian' => '']))); ?>" class="ml-1 text-green-600 hover:text-green-800">×</a>
                        </span>
                    <?php endif; ?>

                    <?php if($isPopular): ?>
                        <span class="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm">
                            <i class="fas fa-star mr-1"></i>Popular
                            <a href="<?php echo e(route('search', array_diff_key(request()->query(), ['popular' => '']))); ?>" class="ml-1 text-yellow-600 hover:text-yellow-800">×</a>
                        </span>
                    <?php endif; ?>

                    <a href="<?php echo e(route('search')); ?>" class="text-orange-600 text-sm font-medium hover:text-orange-800">
                        <i class="fas fa-times mr-1"></i>Clear All
                    </a>
                </div>
            </div>
        <?php endif; ?>


    </div>

    <!-- Results Summary -->
    <div class="mb-6">
        <div class="flex items-center justify-between">
            <div class="text-sm text-gray-600">
                Found <?php echo e($foodItems->total() + $packages->total()); ?> results
                (<?php echo e($foodItems->total()); ?> food items, <?php echo e($packages->total()); ?> packages)
            </div>
        </div>
    </div>

    <!-- Menu Tabs -->
    <div class="mb-6">
        <div class="flex border-b border-gray-200">
            <button onclick="showTab('packages')" id="packages-tab" class="tab-btn px-4 py-2 font-medium text-gray-500 border-b-2 border-transparent hover:text-gray-700 hover:border-gray-300">
                Packages (<?php echo e($packages->total()); ?>)
            </button>
            <button onclick="showTab('food-items')" id="food-items-tab" class="tab-btn px-4 py-2 font-medium text-orange-600 border-b-2 border-orange-600">
                Food Items (<?php echo e($foodItems->total()); ?>)
            </button>
        </div>
    </div>

    <!-- Packages Section -->
    <div id="packages-section" class="hidden">
        <?php if($packages->count() > 0): ?>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                <?php $__currentLoopData = $packages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $package): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="food-card bg-white rounded-lg shadow-sm overflow-hidden">
                    <div class="relative">
                        <?php if($package->image): ?>
                            <img src="<?php echo e($package->image); ?>" alt="<?php echo e($package->name); ?>" class="w-full h-48 object-cover">
                        <?php else: ?>
                            <div class="w-full h-48 bg-gray-200 flex items-center justify-center">
                                <i class="fas fa-image text-gray-400 text-3xl"></i>
                            </div>
                        <?php endif; ?>
                        <?php if($package->is_popular): ?>
                            <span class="absolute top-2 left-2 bg-yellow-500 text-white px-2 py-1 rounded text-xs font-semibold">
                                <i class="fas fa-star mr-1"></i>Popular
                            </span>
                        <?php endif; ?>
                        <?php if($package->discount_percentage > 0): ?>
                            <span class="absolute top-2 right-2 bg-red-500 text-white px-2 py-1 rounded text-xs font-semibold">
                                <?php echo e($package->discount_percentage); ?>% OFF
                            </span>
                        <?php endif; ?>
                    </div>
                    <div class="p-4">
                        <div class="flex items-start justify-between mb-2">
                            <h3 class="font-semibold text-lg"><?php echo e($package->name); ?></h3>
                            <div class="flex space-x-1">
                                <?php if($package->is_vegetarian): ?>
                                    <span class="bg-green-100 text-green-800 px-2 py-1 rounded text-xs">
                                        <i class="fas fa-leaf"></i>
                                    </span>
                                <?php endif; ?>
                            </div>
                        </div>
                        <p class="text-gray-600 text-sm mb-3"><?php echo e($package->description); ?></p>

                        <!-- Package Items Preview -->
                        <?php if($package->foodItems && $package->foodItems->count() > 0): ?>
                            <div class="mb-3">
                                <p class="text-xs text-gray-500 mb-1">Includes:</p>
                                <div class="text-xs text-gray-600">
                                    <?php $__currentLoopData = $package->foodItems->take(3); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <span><?php echo e($item->name); ?><?php echo e(!$loop->last ? ', ' : ''); ?></span>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    <?php if($package->foodItems->count() > 3): ?>
                                        <span class="text-orange-600">+<?php echo e($package->foodItems->count() - 3); ?> more</span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endif; ?>

                        <div class="flex items-center justify-between">
                            <div>
                                <div class="flex items-center space-x-2">
                                    <span class="text-xl font-bold text-orange-600">$<?php echo e(number_format($package->price, 2)); ?></span>
                                    <?php if($package->original_price && $package->original_price > $package->price): ?>
                                        <span class="text-sm text-gray-500 line-through">$<?php echo e(number_format($package->original_price, 2)); ?></span>
                                    <?php endif; ?>
                                </div>
                                <?php if($package->serves_people): ?>
                                    <p class="text-xs text-gray-500">Serves <?php echo e($package->serves_people); ?> people</p>
                                <?php endif; ?>
                            </div>
                            <div class="flex space-x-2">
                                <a href="<?php echo e(route('menu.package', $package->slug)); ?>" class="bg-gray-100 text-gray-700 px-3 py-2 rounded text-sm hover:bg-gray-200 transition-colors">
                                    View Details
                                </a>
                                <button onclick="addToCart('package', <?php echo e($package->id); ?>)" class="bg-orange-600 text-white px-4 py-2 rounded text-sm hover:bg-orange-700 transition-colors">
                                    Add to Cart
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>

            <!-- Packages Pagination -->
            <div class="mb-8">
                <?php echo e($packages->appends(request()->query())->links()); ?>

            </div>
        <?php else: ?>
            <div class="text-center py-12">
                <i class="fas fa-box-open text-gray-400 text-4xl mb-4"></i>
                <p class="text-gray-600">No packages found matching your search criteria.</p>
                <a href="<?php echo e(route('search')); ?>" class="text-orange-600 hover:text-orange-800 mt-2 inline-block">Clear filters and browse all packages</a>
            </div>
        <?php endif; ?>
    </div>

    <!-- Food Items Section -->
    <div id="food-items-section">
        <?php if($foodItems->count() > 0): ?>
            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 search-products-grid mb-8">
                <?php $__currentLoopData = $foodItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="food-card bg-white rounded-lg shadow-sm overflow-hidden">
                    <div class="relative">
                        <?php if($item->image): ?>
                            <img src="<?php echo e($item->image); ?>" alt="<?php echo e($item->name); ?>" class="w-full h-32 object-cover">
                        <?php else: ?>
                            <div class="w-full h-32 bg-gray-200 flex items-center justify-center">
                                <i class="fas fa-image text-gray-400"></i>
                            </div>
                        <?php endif; ?>
                        <div class="absolute top-1 left-1 flex space-x-1">
                            <?php if($item->is_vegetarian): ?>
                                <span class="bg-green-500 text-white px-1 py-0.5 rounded text-xs">
                                    <i class="fas fa-leaf"></i>
                                </span>
                            <?php endif; ?>
                            <?php if($item->is_popular): ?>
                                <span class="bg-yellow-500 text-white px-1 py-0.5 rounded text-xs">
                                    <i class="fas fa-star"></i>
                                </span>
                            <?php endif; ?>
                        </div>
                        <?php if($item->is_spicy): ?>
                            <span class="absolute top-1 right-1 bg-red-500 text-white px-1 py-0.5 rounded text-xs">
                                <i class="fas fa-pepper-hot"></i>
                            </span>
                        <?php endif; ?>
                    </div>
                    <div class="p-3">
                        <h3 class="font-semibold text-sm mb-1"><?php echo e($item->name); ?></h3>
                        <p class="text-xs text-gray-600 mb-2"><?php echo e($item->category->name); ?> • <?php echo e($item->cuisine->name); ?></p>
                        <div class="flex items-center justify-between">
                            <div>
                                <span class="text-sm font-bold text-orange-600">$<?php echo e(number_format($item->price_per_unit, 2)); ?></span>
                                <?php if($item->allow_bulk_order && $item->price_per_kg): ?>
                                    <span class="text-xs text-gray-500 block">$<?php echo e(number_format($item->price_per_kg, 2)); ?>/kg</span>
                                <?php endif; ?>
                            </div>
                            <div class="flex space-x-1">
                                <a href="<?php echo e(route('menu.food-item', $item->slug)); ?>" class="bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs hover:bg-gray-200 transition-colors">
                                    View
                                </a>
                                <button onclick="addToCart('food_item', <?php echo e($item->id); ?>)" class="bg-orange-600 text-white px-2 py-1 rounded text-xs hover:bg-orange-700 transition-colors">
                                    Add
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>

            <!-- Food Items Pagination -->
            <div class="mb-8">
                <?php echo e($foodItems->appends(request()->query())->links()); ?>

            </div>
        <?php else: ?>
            <div class="text-center py-12">
                <i class="fas fa-utensils text-gray-400 text-4xl mb-4"></i>
                <p class="text-gray-600">No food items found matching your search criteria.</p>
                <a href="<?php echo e(route('search')); ?>" class="text-orange-600 hover:text-orange-800 mt-2 inline-block">Clear filters and browse all items</a>
            </div>
        <?php endif; ?>
    </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
    // Tab functionality
    function showTab(tabName) {
        // Hide all sections
        document.getElementById('packages-section').classList.add('hidden');
        document.getElementById('food-items-section').classList.add('hidden');

        // Remove active class from all tabs
        document.getElementById('packages-tab').classList.remove('text-orange-600', 'border-orange-600');
        document.getElementById('packages-tab').classList.add('text-gray-500', 'border-transparent');
        document.getElementById('food-items-tab').classList.remove('text-orange-600', 'border-orange-600');
        document.getElementById('food-items-tab').classList.add('text-gray-500', 'border-transparent');

        // Show selected section and activate tab
        if (tabName === 'packages') {
            document.getElementById('packages-section').classList.remove('hidden');
            document.getElementById('packages-tab').classList.add('text-orange-600', 'border-orange-600');
            document.getElementById('packages-tab').classList.remove('text-gray-500', 'border-transparent');
        } else {
            document.getElementById('food-items-section').classList.remove('hidden');
            document.getElementById('food-items-tab').classList.add('text-orange-600', 'border-orange-600');
            document.getElementById('food-items-tab').classList.remove('text-gray-500', 'border-transparent');
        }
    }

    // Filter update function
    function updateFilter(param, value) {
        const url = new URL(window.location);
        if (value && value !== '') {
            url.searchParams.set(param, value);
        } else {
            url.searchParams.delete(param);
        }
        window.location.href = url.toString();
    }

    // Advanced Filter Sidebar functionality (always visible now)
    function initializeFilterSidebar() {
        const sidebar = document.getElementById('filter-sidebar');
        if (sidebar) {
            // Ensure sidebar is always visible
            sidebar.classList.remove('-translate-x-full');
            sidebar.classList.add('translate-x-0');
        }
    }

    // Initialize default tab and filter sidebar
    document.addEventListener('DOMContentLoaded', function() {
        showTab('food-items');

        // Initialize filter sidebar (always visible)
        initializeFilterSidebar();

        // Add search page class to body for specific styling
        document.body.classList.add('search-page');

        // Ensure sticky footer is always visible
        ensureStickyFooterVisibility();
    });

    // Function to ensure sticky footer is always visible
    function ensureStickyFooterVisibility() {
        const bottomNav = document.querySelector('.bottom-nav');
        const sidebar = document.getElementById('filter-sidebar');

        if (bottomNav && sidebar) {
            // Ensure bottom nav has higher z-index than sidebar
            bottomNav.style.zIndex = '60';
            sidebar.style.zIndex = '30';

            // Add some extra bottom padding to body to ensure footer is always accessible
            document.body.style.paddingBottom = '80px';
        }
    }
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Downloads\r56t7yihu\food-ordering-app\resources\views/search.blade.php ENDPATH**/ ?>