<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

    <title><?php echo $__env->yieldContent('title', 'Food Ordering App'); ?></title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Bootstrap CSS (for dropdown functionality) -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Custom Styles -->
    <style>
        /* Mobile-first responsive design */
        .container-mobile {
            max-width: 100%;
            padding: 0 1rem;
        }

        @media (min-width: 640px) {
            .container-mobile {
                max-width: 640px;
                margin: 0 auto;
            }
        }

        @media (min-width: 768px) {
            .container-mobile {
                max-width: 768px;
            }
        }

        @media (min-width: 1024px) {
            .container-mobile {
                max-width: 1024px;
            }
        }

        /* Sticky bottom navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            z-index: 50;
            background: white;
            border-top: 1px solid #e5e7eb;
            padding: 0.75rem 0;
        }

        /* Profile dropdown */
        .dropdown-menu {
            border: none;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            border-radius: 0.5rem;
            min-width: 200px;
        }

        .dropdown-item {
            padding: 0.75rem 1rem;
            transition: all 0.2s;
        }

        .dropdown-item:hover {
            background-color: #f8f9fa;
            color: #f97316;
        }

        /* Mobile profile menu */
        .mobile-profile-menu {
            position: fixed;
            bottom: 80px;
            right: 1rem;
            background: white;
            border-radius: 0.5rem;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            min-width: 200px;
            z-index: 50;
            display: none;
        }

        /* Food item cards */
        .food-card {
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .food-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        /* Filter buttons */
        .filter-btn {
            transition: all 0.2s;
        }

        .filter-btn.active {
            background: #3b82f6;
            color: white;
        }

        /* Loading spinner */
        .spinner {
            border: 2px solid #f3f3f3;
            border-top: 2px solid #3b82f6;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Advanced Filter Sidebar Styles */
        #filter-sidebar {
            backdrop-filter: blur(10px);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border-right: 2px solid #e2e8f0;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        #filter-sidebar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(249, 115, 22, 0.05) 0%, rgba(239, 68, 68, 0.05) 100%);
            pointer-events: none;
        }

        #filter-sidebar a {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        #filter-sidebar a::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(249, 115, 22, 0.1), transparent);
            transition: left 0.5s;
        }

        #filter-sidebar a:hover {
            transform: translateX(3px);
            box-shadow: 0 4px 12px rgba(249, 115, 22, 0.15);
            border-radius: 6px;
        }

        #filter-sidebar a:hover::before {
            left: 100%;
        }

        /* Enhanced sidebar header */
        #filter-sidebar .sidebar-header {
            background: linear-gradient(135deg, #f97316 0%, #ea580c 50%, #dc2626 100%);
            position: relative;
            overflow: hidden;
        }

        #filter-sidebar .sidebar-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: shimmer 3s ease-in-out infinite;
        }

        @keyframes shimmer {
            0%, 100% { transform: rotate(0deg); }
            50% { transform: rotate(180deg); }
        }

        /* Mobile filter toggle animation */
        #mobile-filter-toggle {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% {
                opacity: 1;
            }
            50% {
                opacity: .8;
            }
        }

        /* Ensure bottom navigation stays above sidebar */
        .bottom-nav {
            z-index: 60 !important; /* Higher than sidebar z-index */
        }

        /* Responsive adjustments - Always show sidebar with 1/6 width */
        @media (max-width: 767px) {
            #filter-sidebar {
                width: 16.666667vw; /* 1/6 of viewport width */
                min-width: 120px; /* Minimum width for usability */
                max-width: 150px; /* Maximum width to prevent overflow */
                transform: translateX(0); /* Always visible */
            }

            #main-content {
                margin-left: 16.666667vw !important; /* Match sidebar width */
                min-margin-left: 120px !important;
                max-margin-left: 150px !important;
            }

            #mobile-filter-toggle {
                display: none; /* Hide toggle button since sidebar is always visible */
            }

            /* Make text even smaller on mobile for better fit */
            #filter-sidebar h3 {
                font-size: 0.625rem;
                line-height: 1.2;
            }

            #filter-sidebar h4 {
                font-size: 0.5rem;
                line-height: 1.2;
            }

            #filter-sidebar a {
                font-size: 0.5rem;
                padding: 0.2rem 0.3rem;
                line-height: 1.2;
            }

            #filter-sidebar p {
                font-size: 0.5rem;
                line-height: 1.2;
            }

            /* Adjust container padding for mobile only when sidebar is present */
            .search-page .container-mobile:not(.search-page-container) {
                padding-left: calc(16.666667vw + 1rem);
                padding-right: 1rem;
            }
        }

        @media (min-width: 768px) and (max-width: 1023px) {
            #filter-sidebar {
                width: 16.666667vw; /* 1/6 of viewport width */
                min-width: 140px;
                max-width: 180px;
                transform: translateX(0); /* Always visible */
            }

            #main-content {
                margin-left: 16.666667vw !important; /* Match sidebar width */
                min-margin-left: 140px !important;
                max-margin-left: 180px !important;
            }

            #mobile-filter-toggle {
                display: none; /* Hide toggle button */
            }

            /* Adjust container padding for tablet only when sidebar is present */
            .search-page .container-mobile:not(.search-page-container) {
                padding-left: calc(16.666667vw + 1rem);
                padding-right: 1rem;
            }
        }

        @media (min-width: 1024px) {
            #filter-sidebar {
                width: 16.666667vw; /* 1/6 of viewport width */
                min-width: 160px;
                max-width: 220px;
                transform: translateX(0);
            }

            #mobile-filter-toggle {
                display: none;
            }

            #main-content {
                margin-left: 16.666667vw !important; /* Match sidebar width */
                min-margin-left: 160px !important;
                max-margin-left: 220px !important;
            }

            /* Adjust container padding for desktop only when sidebar is present */
            .search-page .container-mobile:not(.search-page-container) {
                padding-left: calc(16.666667vw + 1rem);
                padding-right: 1rem;
            }
        }

        /* Ensure products grid doesn't get hidden */
        .food-card {
            position: relative;
            z-index: 1;
        }

        /* Enhanced scrollbar for sidebar */
        #filter-sidebar::-webkit-scrollbar {
            width: 4px;
        }

        #filter-sidebar::-webkit-scrollbar-track {
            background: rgba(0,0,0,0.1);
        }

        #filter-sidebar::-webkit-scrollbar-thumb {
            background: linear-gradient(45deg, #f97316, #ea580c);
            border-radius: 2px;
        }

        #filter-sidebar::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(45deg, #ea580c, #dc2626);
        }
    </style>

    <?php echo $__env->yieldPushContent('styles'); ?>
</head>
<body class="bg-gray-50 pb-20">
    <!-- Header -->
    <header class="bg-white shadow-sm sticky top-0 z-40">
        <div class="container-mobile">
            <div class="flex items-center justify-between py-4">
                <!-- Logo -->
                <div class="flex items-center">
                    <a href="<?php echo e(route('home')); ?>" class="text-2xl font-bold text-orange-600">
                        <i class="fas fa-utensils mr-2"></i>
                        FoodApp
                    </a>
                </div>

                <!-- Search Bar (Hidden on mobile, shown on larger screens) -->
                <div class="hidden md:flex flex-1 max-w-md mx-8">
                    <form action="<?php echo e(route('search')); ?>" method="GET" class="w-full">
                        <div class="relative">
                            <input type="text"
                                   name="q"
                                   value="<?php echo e(request('q')); ?>"
                                   placeholder="Search for food..."
                                   class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent">
                            <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                        </div>
                    </form>
                </div>

                <!-- Profile Icon -->
                <div class="relative">
                    <?php if(auth()->guard()->check()): ?>
                        <div class="dropdown">
                            <button class="text-gray-700 hover:text-orange-600 transition-colors dropdown-toggle" type="button" id="profileDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-user-circle text-xl"></i>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="profileDropdown">
                                <li><h6 class="dropdown-header"><?php echo e(auth()->user()->name); ?></h6></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="#"><i class="fas fa-user me-2"></i>My Profile</a></li>
                                <li><a class="dropdown-item" href="#"><i class="fas fa-receipt me-2"></i>My Orders</a></li>
                                <li><a class="dropdown-item" href="<?php echo e(route('cart.index')); ?>"><i class="fas fa-shopping-cart me-2"></i>My Cart</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <form method="POST" action="<?php echo e(route('logout')); ?>" class="d-inline">
                                        <?php echo csrf_field(); ?>
                                        <button type="submit" class="dropdown-item text-danger">
                                            <i class="fas fa-sign-out-alt me-2"></i>Logout
                                        </button>
                                    </form>
                                </li>
                            </ul>
                        </div>
                    <?php else: ?>
                        <a href="<?php echo e(route('login')); ?>" class="text-gray-700 hover:text-orange-600 transition-colors">
                            <i class="fas fa-user-circle text-xl"></i>
                        </a>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Mobile Search Bar -->
            <div class="md:hidden pb-4">
                <form action="<?php echo e(route('search')); ?>" method="GET">
                    <div class="relative">
                        <input type="text"
                               name="q"
                               value="<?php echo e(request('q')); ?>"
                               placeholder="Search for food..."
                               class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent">
                        <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                    </div>
                </form>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="min-h-screen">
        <?php echo $__env->yieldContent('content'); ?>
    </main>

    <!-- Bottom Navigation -->
    <nav class="bottom-nav">
        <div class="container-mobile">
            <div class="flex justify-around items-center">
                <a href="<?php echo e(route('home')); ?>" class="flex flex-col items-center text-gray-600 hover:text-orange-600 transition-colors <?php echo e(request()->routeIs('home') ? 'text-orange-600' : ''); ?>">
                    <i class="fas fa-home text-lg mb-1"></i>
                    <span class="text-xs">Home</span>
                </a>

                <a href="<?php echo e(route('menu.index')); ?>" class="flex flex-col items-center text-gray-600 hover:text-orange-600 transition-colors <?php echo e(request()->routeIs('menu.*') ? 'text-orange-600' : ''); ?>">
                    <i class="fas fa-utensils text-lg mb-1"></i>
                    <span class="text-xs">Menu</span>
                </a>

                <a href="<?php echo e(route('catering.index')); ?>" class="flex flex-col items-center text-gray-600 hover:text-orange-600 transition-colors <?php echo e(request()->routeIs('catering.*') ? 'text-orange-600' : ''); ?>">
                    <i class="fas fa-concierge-bell text-lg mb-1"></i>
                    <span class="text-xs">Catering</span>
                </a>

                <a href="<?php echo e(route('cart.index')); ?>" class="flex flex-col items-center text-gray-600 hover:text-orange-600 transition-colors <?php echo e(request()->routeIs('cart.*') ? 'text-orange-600' : ''); ?>">
                    <div class="relative">
                        <i class="fas fa-shopping-cart text-lg mb-1"></i>
                        <span id="cart-badge" class="absolute -top-2 -right-2 bg-orange-600 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center hidden">0</span>
                    </div>
                    <span class="text-xs">Cart</span>
                </a>
            </div>
        </div>
    </nav>

    <!-- Mobile Profile Menu -->
    <?php if(auth()->guard()->check()): ?>
        <div id="mobileProfileMenu" class="mobile-profile-menu">
            <div class="p-4 border-b">
                <h6 class="font-semibold text-gray-800"><?php echo e(auth()->user()->name); ?></h6>
                <small class="text-gray-500"><?php echo e(auth()->user()->mobile); ?></small>
            </div>
            <div class="py-2">
                <a href="#" class="flex items-center px-4 py-3 text-gray-700 hover:bg-gray-50 transition-colors">
                    <i class="fas fa-user me-3 text-orange-600"></i>
                    My Profile
                </a>
                <a href="#" class="flex items-center px-4 py-3 text-gray-700 hover:bg-gray-50 transition-colors">
                    <i class="fas fa-receipt me-3 text-orange-600"></i>
                    My Orders
                </a>
                <a href="<?php echo e(route('cart.index')); ?>" class="flex items-center px-4 py-3 text-gray-700 hover:bg-gray-50 transition-colors">
                    <i class="fas fa-shopping-cart me-3 text-orange-600"></i>
                    My Cart
                </a>
                <hr class="my-2">
                <form method="POST" action="<?php echo e(route('logout')); ?>" class="block">
                    <?php echo csrf_field(); ?>
                    <button type="submit" class="flex items-center w-full px-4 py-3 text-red-600 hover:bg-red-50 transition-colors">
                        <i class="fas fa-sign-out-alt me-3"></i>
                        Logout
                    </button>
                </form>
            </div>
        </div>
    <?php endif; ?>

    <!-- Toast Notifications -->
    <div id="toast-container" class="fixed top-20 right-4 z-50 space-y-2"></div>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // CSRF Token setup for AJAX
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });

        // Mobile profile menu toggle
        function toggleMobileProfile() {
            const menu = document.getElementById('mobileProfileMenu');
            if (menu.style.display === 'none' || menu.style.display === '') {
                menu.style.display = 'block';
            } else {
                menu.style.display = 'none';
            }
        }

        // Function to show toast notification
        function showToast(message, type = 'success') {
            const toast = $(`
                <div class="toast bg-${type === 'success' ? 'green' : 'red'}-500 text-white px-4 py-2 rounded-lg shadow-lg transform translate-x-full transition-transform duration-300">
                    ${message}
                </div>
            `);

            $('#toast-container').append(toast);

            setTimeout(() => {
                toast.removeClass('translate-x-full');
            }, 100);

            setTimeout(() => {
                toast.addClass('translate-x-full');
                setTimeout(() => {
                    toast.remove();
                }, 300);
            }, 3000);
        }

        // Update cart count badge
        function updateCartCount() {
            $.get('<?php echo e(route("cart.count")); ?>', function(data) {
                const cartBadge = $('#cart-badge');
                if (data.cart_count > 0) {
                    cartBadge.text(data.cart_count).removeClass('hidden');
                } else {
                    cartBadge.addClass('hidden');
                }
            });
        }

        // Add to cart function (updated to show cart count)
        function addToCart(itemType, itemId, orderType = 'unit') {
            const quantity = parseInt($('#quantity').val()) || 1;
            const unitWeight = parseFloat($('#unit_weight').val()) || null;
            const specialInstructions = $('#special_instructions').val() || '';

            $.post('<?php echo e(route("cart.add")); ?>', {
                item_type: itemType,
                item_id: itemId,
                quantity: quantity,
                order_type: orderType,
                unit_weight: unitWeight,
                special_instructions: specialInstructions
            }, function(data) {
                if (data.success) {
                    showToast(data.message);
                    updateCartCount(); // Update cart count badge
                }
            }).fail(function(xhr) {
                const error = xhr.responseJSON?.error || 'Error adding item to cart';
                showToast(error, 'error');
            });
        }

        // Initialize on page load
        $(document).ready(function() {
            // Initialize cart count
            updateCartCount();

            // Close mobile profile menu when clicking outside
            $(document).click(function(event) {
                const menu = $('#mobileProfileMenu');
                const target = $(event.target);

                if (!target.closest('#mobileProfileMenu').length && !target.closest('[onclick="toggleMobileProfile()"]').length) {
                    menu.hide();
                }
            });
        });
    </script>

    <?php echo $__env->yieldPushContent('scripts'); ?>
</body>
</html>
<?php /**PATH C:\Users\<USER>\Downloads\r56t7yihu\food-ordering-app\resources\views/layouts/app.blade.php ENDPATH**/ ?>